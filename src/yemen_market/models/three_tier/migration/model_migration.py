"""Migration utilities for transitioning from old dual-track to three-tier methodology.

This module provides tools to help migrate existing code and results from the
old track1/track2 approach to the new three-tier methodology.
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, Optional, Union, List
from pathlib import Path
import json
import warnings

from yemen_market.utils.logging import (
    info, warning, error, timer, progress, bind
)
from yemen_market.models.three_tier.integration import ThreeTierAnalysis
from yemen_market.models.three_tier.core.results_container import ResultsContainer

# Set module context
bind(module=__name__)


class ModelMigrationHelper:
    """Helper class for migrating from old models to three-tier methodology.
    
    This class provides utilities to:
    1. Map old model results to new format
    2. Convert old configuration to three-tier config
    3. Validate migration results
    4. Generate migration reports
    """
    
    def __init__(self):
        """Initialize migration helper."""
        self.old_to_new_mapping = {
            'track1_complex': {
                'tvp_vecm': 'tier3_validation.dynamic_factor_model',
                'spatial_network': 'tier3_validation.spatial_pca_analysis'
            },
            'track2_simple': {
                'threshold_vecm': 'tier2_commodity.threshold_vecm'
            },
            'worldbank_threshold_vecm': 'tier2_commodity.threshold_vecm'
        }
        
        self.migration_report = {
            'models_migrated': [],
            'warnings': [],
            'errors': [],
            'recommendations': []
        }
    
    def migrate_configuration(self, old_config: Dict[str, Any]) -> Dict[str, Any]:
        """Convert old model configuration to three-tier format.
        
        Parameters
        ----------
        old_config : dict
            Configuration from old dual-track models
            
        Returns
        -------
        dict
            Configuration for three-tier analysis
        """
        with timer("migrate_configuration"):
            info("Migrating configuration from dual-track to three-tier format")
            
            new_config = {
                'tier1_config': {},
                'tier2_config': {},
                'tier3_config': {}
            }
            
            # Extract common settings
            if 'panel_settings' in old_config:
                new_config['tier1_config']['fixed_effects'] = old_config['panel_settings'].get(
                    'fixed_effects', ['entity', 'time']
                )
                new_config['tier1_config']['cluster_var'] = old_config['panel_settings'].get(
                    'cluster_var', 'entity'
                )
            
            # Map track1 settings to tier3
            if 'track1_config' in old_config:
                track1 = old_config['track1_config']
                
                # TVP-VECM maps to dynamic factor model
                if 'tvp_vecm' in track1:
                    new_config['tier3_config']['n_factors'] = track1['tvp_vecm'].get('n_factors', 3)
                    new_config['tier3_config']['ar_lags'] = track1['tvp_vecm'].get('lags', 1)
                
                # Spatial network maps to spatial PCA
                if 'spatial_network' in track1:
                    new_config['tier3_config']['use_spatial'] = True
            
            # Map track2 settings to tier2
            if 'track2_config' in old_config:
                track2 = old_config['track2_config']
                
                # Threshold VECM settings
                if 'threshold_vecm' in track2:
                    new_config['tier2_config']['test_thresholds'] = True
                    new_config['tier2_config']['max_lags'] = track2['threshold_vecm'].get('max_lags', 4)
                    new_config['tier2_config']['threshold_var'] = track2['threshold_vecm'].get(
                        'threshold_variable', 'conflict_intensity'
                    )
            
            # Add recommended new settings
            new_config['tier1_config']['driscoll_kraay'] = True  # Robust standard errors
            new_config['tier2_config']['min_observations'] = 50  # Per commodity
            new_config['tier3_config']['conflict_validation'] = True  # New validation
            
            # Add output directory
            if 'output_dir' in old_config:
                new_config['output_dir'] = old_config['output_dir']
            
            self.migration_report['warnings'].append(
                "Configuration migrated. Please review new settings for optimal performance."
            )
            
            return new_config
    
    def migrate_results(self, old_results: Union[Dict, Any], 
                       model_type: str) -> ResultsContainer:
        """Convert old model results to new ResultsContainer format.
        
        Parameters
        ----------
        old_results : dict or model results
            Results from old dual-track models
        model_type : str
            Type of old model (e.g., 'threshold_vecm', 'tvp_vecm')
            
        Returns
        -------
        ResultsContainer
            Results in new standardized format
        """
        with timer(f"migrate_results_{model_type}"):
            info(f"Migrating results from {model_type}")
            
            # Determine appropriate tier
            if model_type in ['tvp_vecm', 'spatial_network']:
                tier = 'tier3_validation'
                new_model_type = 'migrated_' + model_type
            elif model_type in ['threshold_vecm', 'worldbank_threshold_vecm']:
                tier = 'tier2_commodity'
                new_model_type = 'threshold_vecm'
            else:
                tier = 'tier1_pooled'
                new_model_type = 'pooled_panel'
            
            # Create new results container
            results = ResultsContainer(tier=tier, model_type=new_model_type)
            
            # Migrate based on model type
            if model_type == 'threshold_vecm':
                self._migrate_threshold_results(old_results, results)
            elif model_type == 'tvp_vecm':
                self._migrate_tvp_results(old_results, results)
            elif model_type == 'spatial_network':
                self._migrate_spatial_results(old_results, results)
            else:
                self._migrate_generic_results(old_results, results)
            
            self.migration_report['models_migrated'].append({
                'old_type': model_type,
                'new_tier': tier,
                'new_type': new_model_type
            })
            
            return results
    
    def _migrate_threshold_results(self, old_results: Any, 
                                  new_results: ResultsContainer) -> None:
        """Migrate threshold VECM results."""
        # Extract coefficients
        if hasattr(old_results, 'params'):
            for i, param in enumerate(old_results.params):
                new_results.add_coefficient(
                    f'param_{i}',
                    float(param),
                    se=float(old_results.bse[i]) if hasattr(old_results, 'bse') else 0.0
                )
        
        # Extract threshold information
        if hasattr(old_results, 'threshold'):
            new_results.add_tier_specific(
                threshold_value=float(old_results.threshold),
                threshold_ci=(
                    float(old_results.threshold_ci[0]),
                    float(old_results.threshold_ci[1])
                ) if hasattr(old_results, 'threshold_ci') else None
            )
        
        # Model fit statistics
        if hasattr(old_results, 'nobs') and hasattr(old_results, 'llf'):
            new_results.set_comparison_metrics(
                n_obs=int(old_results.nobs),
                n_params=len(old_results.params) if hasattr(old_results, 'params') else 0,
                log_lik=float(old_results.llf),
                r2=float(old_results.rsquared) if hasattr(old_results, 'rsquared') else 0.0
            )
    
    def _migrate_tvp_results(self, old_results: Any, 
                           new_results: ResultsContainer) -> None:
        """Migrate time-varying parameter VECM results."""
        # TVP models map to dynamic factor models
        if hasattr(old_results, 'smoothed_states'):
            new_results.add_tier_specific(
                smoothed_factors=old_results.smoothed_states,
                time_varying=True
            )
        
        # Extract any factor loadings
        if hasattr(old_results, 'factor_loadings'):
            new_results.add_tier_specific(
                factor_loadings=old_results.factor_loadings
            )
        
        # Add metadata
        new_results.metadata['original_model'] = 'tvp_vecm'
        new_results.metadata['warnings'].append(
            "TVP-VECM results migrated to dynamic factor format. "
            "Consider re-running with new three-tier methodology for full benefits."
        )
    
    def _migrate_spatial_results(self, old_results: Any, 
                               new_results: ResultsContainer) -> None:
        """Migrate spatial network model results."""
        # Spatial models map to spatial PCA analysis
        if hasattr(old_results, 'spatial_weights'):
            new_results.add_tier_specific(
                spatial_weights=old_results.spatial_weights,
                spatial_correlation=getattr(old_results, 'morans_i', None)
            )
        
        # Network metrics
        if hasattr(old_results, 'network_metrics'):
            new_results.add_tier_specific(
                network_metrics=old_results.network_metrics
            )
        
        new_results.metadata['original_model'] = 'spatial_network'
        new_results.metadata['warnings'].append(
            "Spatial network results migrated. New spatial PCA analysis provides "
            "additional integration insights."
        )
    
    def _migrate_generic_results(self, old_results: Any,
                               new_results: ResultsContainer) -> None:
        """Generic migration for other model types."""
        # Try to extract common attributes
        common_attrs = ['params', 'bse', 'tvalues', 'pvalues', 'rsquared', 
                       'nobs', 'llf', 'aic', 'bic']
        
        migrated_attrs = []
        for attr in common_attrs:
            if hasattr(old_results, attr):
                value = getattr(old_results, attr)
                if attr in ['params', 'bse', 'tvalues', 'pvalues']:
                    # These are typically arrays
                    for i, val in enumerate(value):
                        if attr == 'params':
                            new_results.coefficients[f'param_{i}'] = float(val)
                        elif attr == 'bse':
                            new_results.standard_errors[f'param_{i}'] = float(val)
                else:
                    # Scalar values
                    new_results.metadata[attr] = float(value) if isinstance(value, (int, float)) else value
                migrated_attrs.append(attr)
        
        if migrated_attrs:
            info(f"Migrated attributes: {', '.join(migrated_attrs)}")
        else:
            warning("No standard attributes found for migration")
    
    def validate_migration(self, old_results: Any, new_results: ResultsContainer,
                         tolerance: float = 0.001) -> Dict[str, Any]:
        """Validate that migration preserved key results.
        
        Parameters
        ----------
        old_results : Any
            Original results
        new_results : ResultsContainer
            Migrated results
        tolerance : float
            Tolerance for numerical comparisons
            
        Returns
        -------
        dict
            Validation results
        """
        validation = {
            'valid': True,
            'checks': [],
            'warnings': []
        }
        
        # Check coefficient preservation
        if hasattr(old_results, 'params') and new_results.coefficients:
            old_params = old_results.params
            new_params = [new_results.coefficients.get(f'param_{i}', np.nan) 
                         for i in range(len(old_params))]
            
            if not np.allclose(old_params, new_params, rtol=tolerance, equal_nan=True):
                validation['valid'] = False
                validation['warnings'].append("Coefficient values differ after migration")
            else:
                validation['checks'].append("Coefficients preserved ✓")
        
        # Check model fit statistics
        if hasattr(old_results, 'rsquared') and new_results.comparison_metrics:
            if abs(old_results.rsquared - new_results.comparison_metrics.r_squared) > tolerance:
                validation['valid'] = False
                validation['warnings'].append("R-squared value differs after migration")
            else:
                validation['checks'].append("R-squared preserved ✓")
        
        # Check sample size
        if hasattr(old_results, 'nobs') and new_results.metadata.get('n_observations'):
            if old_results.nobs != new_results.metadata['n_observations']:
                validation['warnings'].append("Sample size mismatch")
        
        return validation
    
    def generate_migration_script(self, script_path: Path) -> str:
        """Generate a migration script for a specific file.
        
        Parameters
        ----------
        script_path : Path
            Path to script that needs migration
            
        Returns
        -------
        str
            Migration code snippet
        """
        info(f"Generating migration code for {script_path}")
        
        migration_code = '''"""
Migration code for transitioning to three-tier methodology.
Add this to your script after imports.
"""

from yemen_market.models.three_tier.integration import ThreeTierAnalysis
from yemen_market.models.three_tier.migration import ModelMigrationHelper

# Initialize migration helper
migrator = ModelMigrationHelper()

# Option 1: Full three-tier analysis (recommended)
def run_three_tier_analysis(data, config=None):
    """Run complete three-tier analysis."""
    # Migrate old config if provided
    if config and 'track1_config' in config:
        config = migrator.migrate_configuration(config)
    
    # Run analysis
    analysis = ThreeTierAnalysis(config)
    results = analysis.run_full_analysis(data)
    
    return results

# Option 2: Migrate existing results
def migrate_old_results(old_model_results, model_type):
    """Convert old results to new format."""
    new_results = migrator.migrate_results(old_model_results, model_type)
    
    # Validate migration
    validation = migrator.validate_migration(old_model_results, new_results)
    if not validation['valid']:
        print(f"Migration warnings: {validation['warnings']}")
    
    return new_results

# Replace old imports:
# from yemen_market.models.track2_simple import ThresholdVECM
# with:
from yemen_market.models.three_tier.tier2_commodity import CommodityExtractor

# For threshold VECM specifically:
extractor = CommodityExtractor(config)
results = extractor.analyze_commodity(data, 'wheat')  # Per commodity
'''
        
        self.migration_report['recommendations'].append(
            f"Migration script generated for {script_path.name}"
        )
        
        return migration_code
    
    def create_migration_report(self, output_path: Optional[Path] = None) -> str:
        """Create comprehensive migration report.
        
        Parameters
        ----------
        output_path : Path, optional
            Path to save report
            
        Returns
        -------
        str
            Migration report content
        """
        report_lines = [
            "# Three-Tier Migration Report",
            f"\nGenerated: {pd.Timestamp.now()}",
            "\n## Summary",
            f"- Models migrated: {len(self.migration_report['models_migrated'])}",
            f"- Warnings: {len(self.migration_report['warnings'])}",
            f"- Errors: {len(self.migration_report['errors'])}",
            "\n## Migration Details"
        ]
        
        # Model migrations
        if self.migration_report['models_migrated']:
            report_lines.append("\n### Models Migrated")
            for migration in self.migration_report['models_migrated']:
                report_lines.append(
                    f"- {migration['old_type']} → {migration['new_tier']}.{migration['new_type']}"
                )
        
        # Warnings
        if self.migration_report['warnings']:
            report_lines.append("\n### Warnings")
            for warning in self.migration_report['warnings']:
                report_lines.append(f"- ⚠️  {warning}")
        
        # Errors
        if self.migration_report['errors']:
            report_lines.append("\n### Errors")
            for error in self.migration_report['errors']:
                report_lines.append(f"- ❌ {error}")
        
        # Recommendations
        report_lines.append("\n## Recommendations")
        report_lines.extend([
            "1. **Re-run analysis**: Use ThreeTierAnalysis for best results",
            "2. **Update imports**: Replace track1/track2 with three_tier modules",
            "3. **Review configuration**: New options available for better performance",
            "4. **Test thoroughly**: Ensure results consistency after migration"
        ])
        
        if self.migration_report['recommendations']:
            report_lines.append("\n### Specific Recommendations")
            for rec in self.migration_report['recommendations']:
                report_lines.append(f"- {rec}")
        
        # Next steps
        report_lines.extend([
            "\n## Next Steps",
            "1. Run comparison tests between old and new results",
            "2. Update documentation to reference three-tier methodology",
            "3. Archive old model files after successful migration",
            "4. Train team on new three-tier approach"
        ])
        
        report_content = '\n'.join(report_lines)
        
        if output_path:
            output_path.write_text(report_content)
            info(f"Migration report saved to {output_path}")
        
        return report_content


def compare_methodologies(data: pd.DataFrame, 
                         old_config: Dict[str, Any],
                         output_dir: Optional[Path] = None) -> Dict[str, Any]:
    """Run both old and new methodologies for comparison.
    
    Parameters
    ----------
    data : pd.DataFrame
        Input data
    old_config : dict
        Configuration for old models
    output_dir : Path, optional
        Directory for comparison outputs
        
    Returns
    -------
    dict
        Comparison results
    """
    with timer("methodology_comparison"):
        info("Running methodology comparison")
        
        comparison = {
            'old_results': {},
            'new_results': {},
            'differences': {},
            'recommendations': []
        }
        
        # Note: This is a placeholder for comparison logic
        # Actual implementation would run both methodologies
        
        warning("Full comparison requires old models to be available. "
               "Please run old models separately if needed.")
        
        # Run new methodology
        migrator = ModelMigrationHelper()
        new_config = migrator.migrate_configuration(old_config)
        
        analysis = ThreeTierAnalysis(new_config)
        comparison['new_results'] = analysis.run_full_analysis(data)
        
        # Generate comparison report
        if output_dir:
            report_path = output_dir / "methodology_comparison.md"
            report_lines = [
                "# Methodology Comparison Report",
                "\n## Three-Tier Advantages",
                "- Unified framework for all market-commodity pairs",
                "- Better handling of 3D panel structure",
                "- Cross-tier validation ensures consistency",
                "- Factor analysis provides deeper insights",
                "- Conflict validation integrated",
                "\n## Migration Recommendations",
                "- Use three-tier for all new analyses",
                "- Gradually migrate existing workflows",
                "- Keep old results for validation",
                "- Document any discrepancies"
            ]
            report_path.write_text('\n'.join(report_lines))
        
        return comparison