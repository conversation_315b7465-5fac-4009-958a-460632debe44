"""Data validation utilities for three-tier panel analysis.

This module provides comprehensive validation functions to ensure data
quality and compatibility with econometric requirements.
"""

import pandas as pd
import numpy as np
from typing import List, Tuple, Dict, Optional, Set
from yemen_market.utils.logging import info, warning, error, bind

# Set module context
bind(module=__name__)


class PanelDataValidator:
    """Validator for panel data structures and quality checks."""
    
    def __init__(self):
        """Initialize validator with standard requirements."""
        self.min_obs_per_entity = 10
        self.min_entities = 5
        self.max_missing_pct = 0.5
        self.required_variance = 1e-10
        
    def validate_panel_structure(self, df: pd.DataFrame, 
                               entity_col: str, time_col: str) -> Dict[str, bool]:
        """Validate panel data structure.
        
        Parameters
        ----------
        df : pd.DataFrame
            Panel data
        entity_col : str
            Entity identifier column
        time_col : str
            Time identifier column
            
        Returns
        -------
        dict
            Validation results with pass/fail for each check
        """
        results = {}
        
        # Check if columns exist
        results['columns_exist'] = all(col in df.columns for col in [entity_col, time_col])
        if not results['columns_exist']:
            error(f"Missing required columns: {entity_col} or {time_col}")
            return results
        
        # Check for duplicates
        duplicates = df.duplicated(subset=[entity_col, time_col])
        results['no_duplicates'] = not duplicates.any()
        if duplicates.any():
            warning(f"Found {duplicates.sum()} duplicate observations")
        
        # Check entity counts
        entity_counts = df.groupby(entity_col).size()
        results['sufficient_obs_per_entity'] = (entity_counts >= self.min_obs_per_entity).all()
        if not results['sufficient_obs_per_entity']:
            low_count_entities = entity_counts[entity_counts < self.min_obs_per_entity]
            warning(f"{len(low_count_entities)} entities have < {self.min_obs_per_entity} observations")
        
        # Check total entities
        n_entities = df[entity_col].nunique()
        results['sufficient_entities'] = n_entities >= self.min_entities
        if not results['sufficient_entities']:
            error(f"Only {n_entities} entities (minimum: {self.min_entities})")
        
        # Check balance
        is_balanced = entity_counts.nunique() == 1
        results['is_balanced'] = is_balanced
        if not is_balanced:
            info(f"Unbalanced panel: observations range from {entity_counts.min()} to {entity_counts.max()}")
        
        # Overall pass
        results['overall_pass'] = all(v for k, v in results.items() if k != 'is_balanced')
        
        return results
    
    def validate_time_series_properties(self, df: pd.DataFrame, 
                                      entity_col: str, time_col: str,
                                      value_col: str) -> Dict[str, bool]:
        """Validate time series properties within panel.
        
        Parameters
        ----------
        df : pd.DataFrame
            Panel data
        entity_col : str
            Entity identifier
        time_col : str
            Time identifier  
        value_col : str
            Value column to check
            
        Returns
        -------
        dict
            Validation results
        """
        results = {}
        
        # Check time gaps
        time_gaps = []
        for entity in df[entity_col].unique():
            entity_data = df[df[entity_col] == entity].sort_values(time_col)
            if len(entity_data) > 1:
                gaps = entity_data[time_col].diff().dropna()
                if not gaps.empty and gaps.nunique() > 1:
                    time_gaps.append(entity)
        
        results['regular_time_intervals'] = len(time_gaps) == 0
        if time_gaps:
            warning(f"{len(time_gaps)} entities have irregular time intervals")
        
        # Check missing values
        missing_pct = df[value_col].isna().sum() / len(df)
        results['acceptable_missing'] = missing_pct <= self.max_missing_pct
        if missing_pct > 0:
            info(f"Missing values: {missing_pct:.1%}")
        
        # Check variance
        low_variance_entities = []
        for entity in df[entity_col].unique():
            entity_vals = df[df[entity_col] == entity][value_col].dropna()
            if len(entity_vals) > 1 and entity_vals.var() < self.required_variance:
                low_variance_entities.append(entity)
        
        results['sufficient_variance'] = len(low_variance_entities) == 0
        if low_variance_entities:
            warning(f"{len(low_variance_entities)} entities have near-zero variance")
        
        # Check for extreme values
        if df[value_col].notna().any():
            z_scores = np.abs((df[value_col] - df[value_col].mean()) / df[value_col].std())
            extreme_count = (z_scores > 5).sum()
            results['no_extreme_outliers'] = extreme_count == 0
            if extreme_count > 0:
                warning(f"Found {extreme_count} extreme outliers (|z| > 5)")
        else:
            results['no_extreme_outliers'] = True
        
        # Overall pass
        results['overall_pass'] = all(results.values())
        
        return results
    
    def validate_for_tier1(self, df: pd.DataFrame) -> Tuple[bool, List[str]]:
        """Validate data for Tier 1 pooled panel analysis.
        
        Parameters
        ----------
        df : pd.DataFrame
            Input data with market, commodity, date, and price columns
            
        Returns
        -------
        tuple
            (is_valid, list_of_issues)
        """
        issues = []
        
        # Check required columns
        required = ['governorate', 'commodity', 'date', 'usd_price']
        missing = set(required) - set(df.columns)
        if missing:
            issues.append(f"Missing columns: {missing}")
            return False, issues
        
        # Create entity column
        df_copy = df.copy()
        df_copy['entity'] = df_copy['governorate'] + "_" + df_copy['commodity']
        
        # Validate panel structure
        struct_results = self.validate_panel_structure(df_copy, 'entity', 'date')
        if not struct_results['overall_pass']:
            issues.extend([k for k, v in struct_results.items() if not v and k != 'overall_pass'])
        
        # Validate time series properties
        ts_results = self.validate_time_series_properties(df_copy, 'entity', 'date', 'usd_price')
        if not ts_results['overall_pass']:
            issues.extend([k for k, v in ts_results.items() if not v and k != 'overall_pass'])
        
        # Check minimum panel dimensions
        n_entities = df_copy['entity'].nunique()
        n_periods = df_copy['date'].nunique()
        
        if n_entities < 10:
            issues.append(f"Too few entities: {n_entities} (minimum: 10)")
        if n_periods < 20:
            issues.append(f"Too few time periods: {n_periods} (minimum: 20)")
        
        is_valid = len(issues) == 0
        if is_valid:
            info("Data validated successfully for Tier 1 analysis")
        else:
            error(f"Data validation failed with {len(issues)} issues")
            
        return is_valid, issues
    
    def validate_for_tier2(self, df: pd.DataFrame, commodity: str) -> Tuple[bool, List[str]]:
        """Validate data for Tier 2 commodity-specific analysis.
        
        Parameters
        ----------
        df : pd.DataFrame
            Input data
        commodity : str
            Commodity to analyze
            
        Returns
        -------
        tuple
            (is_valid, list_of_issues)
        """
        issues = []
        
        # Filter for commodity
        commodity_df = df[df['commodity'] == commodity]
        
        if commodity_df.empty:
            issues.append(f"No data for commodity: {commodity}")
            return False, issues
        
        # Validate panel structure
        struct_results = self.validate_panel_structure(commodity_df, 'governorate', 'date')
        if not struct_results['overall_pass']:
            issues.extend([k for k, v in struct_results.items() if not v and k != 'overall_pass'])
        
        # Check minimum requirements for VECM
        n_markets = commodity_df['governorate'].nunique()
        n_periods = commodity_df['date'].nunique()
        
        if n_markets < 3:
            issues.append(f"Too few markets for {commodity}: {n_markets} (minimum: 3)")
        if n_periods < 50:
            issues.append(f"Too few periods for {commodity}: {n_periods} (minimum: 50)")
        
        # Check for sufficient price variation
        price_cv = commodity_df.groupby('governorate')['usd_price'].apply(
            lambda x: x.std() / x.mean() if x.mean() > 0 else 0
        )
        low_variation_markets = price_cv[price_cv < 0.05]
        if len(low_variation_markets) > 0:
            issues.append(f"{len(low_variation_markets)} markets have CV < 5% for {commodity}")
        
        is_valid = len(issues) == 0
        if is_valid:
            info(f"Data validated successfully for Tier 2 analysis of {commodity}")
        else:
            error(f"Data validation failed for {commodity} with {len(issues)} issues")
            
        return is_valid, issues
    
    def validate_for_tier3(self, df: pd.DataFrame) -> Tuple[bool, List[str]]:
        """Validate data for Tier 3 factor analysis.
        
        Parameters
        ----------
        df : pd.DataFrame
            Input data in wide format (time × series)
            
        Returns
        -------
        tuple
            (is_valid, list_of_issues)
        """
        issues = []
        
        # Check minimum dimensions
        n_periods, n_series = df.shape
        
        if n_periods < 50:
            issues.append(f"Too few time periods: {n_periods} (minimum: 50)")
        if n_series < 10:
            issues.append(f"Too few series: {n_series} (minimum: 10)")
        
        # Check missing data
        missing_pct = df.isna().sum().sum() / (n_periods * n_series)
        if missing_pct > 0.3:
            issues.append(f"Too many missing values: {missing_pct:.1%} (maximum: 30%)")
        
        # Check for constant series
        constant_series = []
        for col in df.columns:
            if df[col].nunique() <= 1:
                constant_series.append(col)
        
        if constant_series:
            issues.append(f"{len(constant_series)} series have no variation")
        
        # Check correlation structure
        if n_series >= 2 and not df.empty:
            # Get pairwise correlations for non-missing data
            corr_matrix = df.corr()
            # Check if there's some correlation structure
            off_diagonal = corr_matrix.values[~np.eye(corr_matrix.shape[0], dtype=bool)]
            if np.nanmean(np.abs(off_diagonal)) < 0.1:
                warning("Low average correlation between series - factor analysis may not be appropriate")
        
        is_valid = len(issues) == 0
        if is_valid:
            info("Data validated successfully for Tier 3 factor analysis")
        else:
            error(f"Data validation failed with {len(issues)} issues")
            
        return is_valid, issues
    
    def check_data_quality(self, df: pd.DataFrame) -> pd.DataFrame:
        """Generate comprehensive data quality report.
        
        Parameters
        ----------
        df : pd.DataFrame
            Input data
            
        Returns
        -------
        pd.DataFrame
            Quality metrics by variable
        """
        quality_report = []
        
        for col in df.columns:
            col_data = df[col]
            
            # Skip non-numeric columns for some metrics
            if pd.api.types.is_numeric_dtype(col_data):
                metrics = {
                    'variable': col,
                    'dtype': str(col_data.dtype),
                    'n_obs': len(col_data),
                    'n_missing': col_data.isna().sum(),
                    'pct_missing': col_data.isna().sum() / len(col_data) * 100,
                    'n_unique': col_data.nunique(),
                    'mean': col_data.mean() if not col_data.empty else np.nan,
                    'std': col_data.std() if not col_data.empty else np.nan,
                    'min': col_data.min() if not col_data.empty else np.nan,
                    'max': col_data.max() if not col_data.empty else np.nan,
                    'n_zeros': (col_data == 0).sum(),
                    'n_negative': (col_data < 0).sum() if pd.api.types.is_numeric_dtype(col_data) else 0
                }
            else:
                metrics = {
                    'variable': col,
                    'dtype': str(col_data.dtype),
                    'n_obs': len(col_data),
                    'n_missing': col_data.isna().sum(),
                    'pct_missing': col_data.isna().sum() / len(col_data) * 100,
                    'n_unique': col_data.nunique(),
                    'mean': np.nan,
                    'std': np.nan,
                    'min': np.nan,
                    'max': np.nan,
                    'n_zeros': np.nan,
                    'n_negative': np.nan
                }
            
            quality_report.append(metrics)
        
        return pd.DataFrame(quality_report)