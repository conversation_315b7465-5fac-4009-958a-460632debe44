"""Panel data handler for 3D market × commodity × time structures.

This module provides utilities to transform 3D panel data into formats
suitable for different tiers of analysis.
"""

import pandas as pd
import numpy as np
from typing import Tuple, List, Optional, Dict, Union
from yemen_market.utils.logging import (
    info, warning, error, timer, log_data_shape, bind
)

# Set module context
bind(module=__name__)


class PanelDataHandler:
    """Central handler for 3D panel data operations.
    
    This class manages the transformation of 3D panel data (market × commodity × time)
    into appropriate formats for each tier of analysis:
    - Tier 1: Entity-time panels (market-commodity pairs)
    - Tier 2: 2D panels for each commodity
    - Tier 3: Wide matrices for factor analysis
    """
    
    def __init__(self):
        """Initialize the panel data handler."""
        self.required_columns = ['governorate', 'commodity', 'date', 'usd_price']
        self.entity_col = 'entity'  # For Tier 1
        self.market_col = 'governorate'
        self.commodity_col = 'commodity'
        self.time_col = 'date'
        self.price_col = 'usd_price'
        
    def validate_3d_structure(self, df: pd.DataFrame) -> bool:
        """Validate that dataframe has proper 3D panel structure.
        
        Parameters
        ----------
        df : pd.DataFrame
            Input dataframe to validate
            
        Returns
        -------
        bool
            True if valid 3D structure, False otherwise
        """
        with timer("validate_3d_structure"):
            # Check required columns
            missing_cols = set(self.required_columns) - set(df.columns)
            if missing_cols:
                error(f"Missing required columns: {missing_cols}")
                return False
            
            # Check data types
            if not pd.api.types.is_datetime64_any_dtype(df[self.time_col]):
                warning(f"{self.time_col} is not datetime, attempting conversion")
                try:
                    df[self.time_col] = pd.to_datetime(df[self.time_col])
                except Exception as e:
                    error(f"Failed to convert {self.time_col} to datetime: {e}")
                    return False
            
            # Log structure info
            n_markets = df[self.market_col].nunique()
            n_commodities = df[self.commodity_col].nunique()
            n_periods = df[self.time_col].nunique()
            n_obs = len(df)
            
            info(f"3D Panel Structure: {n_markets} markets × {n_commodities} commodities × {n_periods} periods = {n_obs} observations")
            
            # Check for duplicates
            duplicates = df.duplicated(subset=[self.market_col, self.commodity_col, self.time_col])
            if duplicates.any():
                n_dups = duplicates.sum()
                warning(f"Found {n_dups} duplicate observations")
                
            # Calculate coverage
            theoretical_obs = n_markets * n_commodities * n_periods
            coverage = (n_obs / theoretical_obs) * 100 if theoretical_obs > 0 else 0
            info(f"Panel coverage: {coverage:.1f}% ({n_obs}/{theoretical_obs} possible observations)")
            
            return True
    
    def create_entity_panel(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create entity-time panel for Tier 1 analysis.
        
        Transforms 3D data into 2D by creating market-commodity entities.
        
        Parameters
        ----------
        df : pd.DataFrame
            Input 3D panel data
            
        Returns
        -------
        pd.DataFrame
            2D panel with entity-time structure
        """
        with timer("create_entity_panel"):
            # Validate input
            if not self.validate_3d_structure(df):
                raise ValueError("Invalid 3D panel structure")
            
            # Create copy to avoid modifying original
            panel_df = df.copy()
            
            # Create entity identifier
            panel_df[self.entity_col] = (
                panel_df[self.market_col].astype(str) + "_" + 
                panel_df[self.commodity_col].astype(str)
            )
            
            # Sort by entity and time
            panel_df = panel_df.sort_values([self.entity_col, self.time_col])
            
            # Add entity and time indices
            panel_df['entity_id'] = pd.Categorical(panel_df[self.entity_col]).codes
            panel_df['time_id'] = pd.factorize(panel_df[self.time_col])[0]
            
            # Log transformation info
            n_entities = panel_df[self.entity_col].nunique()
            n_periods = panel_df[self.time_col].nunique()
            log_data_shape("entity_panel", panel_df)
            info(f"Created entity panel: {n_entities} entities × {n_periods} periods")
            
            # Check balance
            entity_counts = panel_df.groupby(self.entity_col).size()
            if entity_counts.min() < entity_counts.max():
                info(f"Unbalanced panel: entity observations range from {entity_counts.min()} to {entity_counts.max()}")
            else:
                info("Balanced panel detected")
                
            return panel_df
    
    def extract_commodity_panel(self, df: pd.DataFrame, commodity: str) -> pd.DataFrame:
        """Extract 2D panel for a specific commodity (Tier 2).
        
        Parameters
        ----------
        df : pd.DataFrame
            Input 3D panel data
        commodity : str
            Commodity to extract
            
        Returns
        -------
        pd.DataFrame
            2D panel for the specified commodity
        """
        with timer(f"extract_commodity_panel_{commodity}"):
            # Filter for commodity
            commodity_df = df[df[self.commodity_col] == commodity].copy()
            
            if commodity_df.empty:
                error(f"No data found for commodity: {commodity}")
                return pd.DataFrame()
            
            # Sort by market and time
            commodity_df = commodity_df.sort_values([self.market_col, self.time_col])
            
            # Log extraction info
            n_markets = commodity_df[self.market_col].nunique()
            n_periods = commodity_df[self.time_col].nunique()
            n_obs = len(commodity_df)
            
            log_data_shape(f"commodity_panel_{commodity}", commodity_df)
            info(f"Extracted {commodity} panel: {n_markets} markets × {n_periods} periods = {n_obs} observations")
            
            # Check coverage for this commodity
            theoretical_obs = n_markets * n_periods
            coverage = (n_obs / theoretical_obs) * 100 if theoretical_obs > 0 else 0
            info(f"{commodity} coverage: {coverage:.1f}%")
            
            return commodity_df
    
    def create_wide_matrix(self, df: pd.DataFrame, value_col: str = None) -> pd.DataFrame:
        """Create wide matrix for factor analysis (Tier 3).
        
        Transforms data into wide format with time as rows and 
        market-commodity pairs as columns.
        
        Parameters
        ----------
        df : pd.DataFrame
            Input panel data
        value_col : str, optional
            Column to use as values (default: usd_price)
            
        Returns
        -------
        pd.DataFrame
            Wide matrix suitable for factor analysis
        """
        with timer("create_wide_matrix"):
            if value_col is None:
                value_col = self.price_col
                
            # Create entity column if not exists
            if self.entity_col not in df.columns:
                df = self.create_entity_panel(df)
            
            # Pivot to wide format
            wide_df = df.pivot(
                index=self.time_col,
                columns=self.entity_col,
                values=value_col
            )
            
            # Log matrix info
            n_periods, n_series = wide_df.shape
            missing_pct = (wide_df.isna().sum().sum() / (n_periods * n_series)) * 100
            
            log_data_shape("wide_matrix", wide_df)
            info(f"Created wide matrix: {n_periods} periods × {n_series} series")
            info(f"Missing values: {missing_pct:.1f}%")
            
            # Check which series have too many missing values
            series_missing = wide_df.isna().sum() / n_periods
            high_missing = series_missing[series_missing > 0.5]
            if len(high_missing) > 0:
                warning(f"{len(high_missing)} series have >50% missing values")
                
            return wide_df
    
    def get_panel_info(self, df: pd.DataFrame) -> Dict[str, Union[int, float, List[str]]]:
        """Get comprehensive information about panel structure.
        
        Parameters
        ----------
        df : pd.DataFrame
            Input panel data
            
        Returns
        -------
        dict
            Dictionary with panel statistics
        """
        info_dict = {
            'n_observations': len(df),
            'n_markets': df[self.market_col].nunique(),
            'n_commodities': df[self.commodity_col].nunique(),
            'n_periods': df[self.time_col].nunique(),
            'date_range': (df[self.time_col].min(), df[self.time_col].max()),
            'markets': sorted(df[self.market_col].unique()),
            'commodities': sorted(df[self.commodity_col].unique()),
        }
        
        # Calculate coverage
        theoretical_obs = (info_dict['n_markets'] * 
                          info_dict['n_commodities'] * 
                          info_dict['n_periods'])
        info_dict['coverage_pct'] = (info_dict['n_observations'] / theoretical_obs * 100 
                                     if theoretical_obs > 0 else 0)
        
        # Check balance by commodity
        commodity_obs = df.groupby(self.commodity_col).size()
        info_dict['min_obs_commodity'] = commodity_obs.idxmin()
        info_dict['max_obs_commodity'] = commodity_obs.idxmax()
        info_dict['balanced'] = commodity_obs.min() == commodity_obs.max()
        
        return info_dict
    
    def handle_missing_data(self, df: pd.DataFrame, method: str = 'forward_fill',
                           max_gap: int = 2) -> pd.DataFrame:
        """Handle missing data in panel.
        
        Parameters
        ----------
        df : pd.DataFrame
            Input panel data
        method : str
            Method to handle missing data ('forward_fill', 'interpolate', 'drop')
        max_gap : int
            Maximum gap size to fill
            
        Returns
        -------
        pd.DataFrame
            Panel with missing data handled
        """
        with timer(f"handle_missing_data_{method}"):
            initial_missing = df[self.price_col].isna().sum()
            info(f"Initial missing values: {initial_missing}")
            
            if method == 'drop':
                clean_df = df.dropna(subset=[self.price_col])
            
            elif method == 'forward_fill':
                # Forward fill within each entity
                clean_df = df.copy()
                clean_df[self.price_col] = (
                    clean_df.groupby([self.market_col, self.commodity_col])[self.price_col]
                    .ffill(limit=max_gap)
                )
            
            elif method == 'interpolate':
                # Linear interpolation within each entity
                clean_df = df.copy()
                clean_df[self.price_col] = (
                    clean_df.groupby([self.market_col, self.commodity_col])[self.price_col]
                    .apply(lambda x: x.interpolate(method='linear', limit=max_gap))
                )
            
            else:
                raise ValueError(f"Unknown method: {method}")
            
            final_missing = clean_df[self.price_col].isna().sum()
            info(f"Final missing values: {final_missing} (removed {initial_missing - final_missing})")
            
            return clean_df