"""Base model class for three-tier econometric methodology.

Adapted from the original base.py to support 3D panel data structures
and the specific requirements of the three-tier approach.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Tuple, Union
import pandas as pd
import numpy as np
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path

from yemen_market.utils.logging import (
    bind, timer, info, warning, error, log_metric, log_data_shape
)

# Set module context
bind(module=__name__)


class TierType(Enum):
    """Enumeration of analysis tiers."""
    TIER1_POOLED = "tier1_pooled_panel"
    TIER2_COMMODITY = "tier2_commodity_specific"
    TIER3_VALIDATION = "tier3_factor_validation"


@dataclass
class ThreeTierResults:
    """Container for results from three-tier analysis."""
    
    # Tier identification
    tier: TierType
    commodity: Optional[str] = None  # For Tier 2
    
    # Common elements
    coefficients: Dict[str, float] = field(default_factory=dict)
    standard_errors: Dict[str, float] = field(default_factory=dict)
    t_statistics: Dict[str, float] = field(default_factory=dict)
    p_values: Dict[str, float] = field(default_factory=dict)
    
    # Model diagnostics
    n_observations: int = 0
    n_entities: Optional[int] = None
    n_periods: Optional[int] = None
    r_squared: float = 0.0
    r_squared_within: Optional[float] = None
    r_squared_between: Optional[float] = None
    
    # Statistical tests
    f_statistic: Optional[float] = None
    f_p_value: Optional[float] = None
    hausman_statistic: Optional[float] = None
    hausman_p_value: Optional[float] = None
    
    # Tier 1 specific
    fixed_effects: Optional[Dict[str, np.ndarray]] = None
    clustered_se_type: Optional[str] = None
    
    # Tier 2 specific  
    threshold_value: Optional[float] = None
    threshold_ci: Optional[Tuple[float, float]] = None
    regime_splits: Optional[Dict[str, int]] = None
    cointegration_rank: Optional[int] = None
    
    # Tier 3 specific
    n_factors: Optional[int] = None
    factor_loadings: Optional[np.ndarray] = None
    variance_explained: Optional[np.ndarray] = None
    conflict_correlation: Optional[float] = None
    
    # Metadata
    estimation_time: float = 0.0
    convergence_achieved: bool = True
    warnings: List[str] = field(default_factory=list)
    
    def summary(self) -> str:
        """Generate summary string of results."""
        lines = [
            f"\n{self.tier.value} Results",
            "=" * 50
        ]
        
        if self.commodity:
            lines.append(f"Commodity: {self.commodity}")
            
        lines.extend([
            f"Observations: {self.n_observations}",
            f"R-squared: {self.r_squared:.4f}"
        ])
        
        if self.r_squared_within is not None:
            lines.append(f"R-squared (within): {self.r_squared_within:.4f}")
            
        if self.coefficients:
            lines.extend(["\nCoefficients:", "-" * 20])
            for var, coef in self.coefficients.items():
                se = self.standard_errors.get(var, np.nan)
                t_stat = self.t_statistics.get(var, np.nan)
                p_val = self.p_values.get(var, np.nan)
                lines.append(
                    f"{var:20} {coef:10.4f} ({se:8.4f})  "
                    f"t={t_stat:6.2f}  p={p_val:6.4f}"
                )
        
        if self.warnings:
            lines.extend(["\nWarnings:", "-" * 20])
            lines.extend(self.warnings)
            
        return "\n".join(lines)


class BaseThreeTierModel(ABC):
    """Abstract base class for three-tier econometric models.
    
    This class provides the interface and common functionality for all
    models in the three-tier methodology.
    """
    
    def __init__(self, tier: TierType, config: Optional[Dict[str, Any]] = None):
        """Initialize base model.
        
        Parameters
        ----------
        tier : TierType
            Which tier this model implements
        config : dict, optional
            Model configuration parameters
        """
        self.tier = tier
        self.config = config or {}
        self.results: Optional[ThreeTierResults] = None
        self.data: Optional[pd.DataFrame] = None
        self.is_fitted = False
        
        # Set up logging context
        bind(model=self.__class__.__name__, tier=tier.value)
        info(f"Initialized {self.__class__.__name__} for {tier.value}")
        
    @abstractmethod
    def validate_data(self, data: pd.DataFrame) -> bool:
        """Validate input data for model requirements.
        
        Parameters
        ----------
        data : pd.DataFrame
            Input data to validate
            
        Returns
        -------
        bool
            True if data is valid, False otherwise
        """
        pass
    
    @abstractmethod
    def fit(self, data: pd.DataFrame, **kwargs) -> 'BaseThreeTierModel':
        """Fit the model to data.
        
        Parameters
        ----------
        data : pd.DataFrame
            Panel data to fit
        **kwargs
            Additional model-specific parameters
            
        Returns
        -------
        self
            Fitted model instance
        """
        pass
    
    @abstractmethod
    def predict(self, data: Optional[pd.DataFrame] = None) -> pd.Series:
        """Generate predictions from fitted model.
        
        Parameters
        ----------
        data : pd.DataFrame, optional
            New data for prediction. If None, uses training data
            
        Returns
        -------
        pd.Series
            Model predictions
        """
        pass
    
    def get_results(self) -> ThreeTierResults:
        """Get model results.
        
        Returns
        -------
        ThreeTierResults
            Results container
            
        Raises
        ------
        ValueError
            If model is not fitted
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted before getting results")
        return self.results
    
    def save_results(self, path: Union[str, Path]) -> None:
        """Save model results to file.
        
        Parameters
        ----------
        path : str or Path
            Path to save results
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted before saving results")
            
        path = Path(path)
        path.parent.mkdir(parents=True, exist_ok=True)
        
        # Convert results to dict for serialization
        results_dict = {
            'tier': self.results.tier.value,
            'commodity': self.results.commodity,
            'coefficients': self.results.coefficients,
            'standard_errors': self.results.standard_errors,
            'diagnostics': {
                'n_observations': self.results.n_observations,
                'r_squared': self.results.r_squared,
                'r_squared_within': self.results.r_squared_within,
            },
            'warnings': self.results.warnings
        }
        
        # Save based on file extension
        if path.suffix == '.json':
            import json
            with open(path, 'w') as f:
                json.dump(results_dict, f, indent=2)
        else:
            # Default to pickle for complex objects
            import pickle
            with open(path, 'wb') as f:
                pickle.dump(self.results, f)
                
        info(f"Saved results to {path}")
    
    def _log_estimation_start(self, data: pd.DataFrame) -> None:
        """Log information at start of estimation."""
        log_data_shape("input_data", data)
        info(f"Starting {self.tier.value} estimation")
        
        # Log configuration
        if self.config:
            info(f"Configuration: {self.config}")
    
    def _log_estimation_complete(self, elapsed_time: float) -> None:
        """Log information after estimation completes."""
        info(f"Estimation complete in {elapsed_time:.2f} seconds")
        
        if self.results:
            if self.results.comparison_metrics:
                log_metric("r_squared", self.results.comparison_metrics.r_squared)
            if self.results.metadata.get('n_observations'):
                log_metric("n_observations", self.results.metadata['n_observations'])
            
            if self.results.metadata.get('warnings'):
                for warn in self.results.metadata['warnings']:
                    warning(warn)