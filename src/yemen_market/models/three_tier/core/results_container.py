"""Standardized results containers for three-tier methodology.

This module provides consistent result structures across all tiers,
ensuring uniform output format and easy comparison between models.
"""

from dataclasses import dataclass, field, asdict
from typing import Dict, List, Optional, Tuple, Union, Any
import pandas as pd
import numpy as np
from pathlib import Path
import json

from yemen_market.utils.logging import info, bind

# Set module context
bind(module=__name__)


@dataclass
class ResultsSummaryStats:
    """Summary statistics for model results."""
    mean: float
    std: float
    min: float
    max: float
    q25: float
    q50: float
    q75: float
    
    @classmethod
    def from_series(cls, series: pd.Series) -> 'ResultsSummaryStats':
        """Create from pandas Series."""
        return cls(
            mean=series.mean(),
            std=series.std(),
            min=series.min(),
            max=series.max(),
            q25=series.quantile(0.25),
            q50=series.quantile(0.50),
            q75=series.quantile(0.75)
        )


@dataclass 
class DiagnosticTests:
    """Container for diagnostic test results."""
    # Residual tests
    jarque_bera: Optional[Tuple[float, float]] = None  # statistic, p-value
    breusch_pagan: Optional[Tuple[float, float]] = None
    durbin_watson: Optional[float] = None
    ljung_box: Optional[Tuple[float, float]] = None
    
    # Model specification tests
    reset_test: Optional[Tuple[float, float]] = None
    hausman_test: Optional[Tuple[float, float]] = None
    
    # Panel-specific tests
    pesaran_cd: Optional[Tuple[float, float]] = None  # cross-sectional dependence
    wooldridge_ar1: Optional[Tuple[float, float]] = None  # serial correlation
    
    def to_dataframe(self) -> pd.DataFrame:
        """Convert diagnostic tests to DataFrame."""
        tests = []
        
        if self.jarque_bera:
            tests.append(('Jarque-Bera', self.jarque_bera[0], self.jarque_bera[1]))
        if self.breusch_pagan:
            tests.append(('Breusch-Pagan', self.breusch_pagan[0], self.breusch_pagan[1]))
        if self.durbin_watson:
            tests.append(('Durbin-Watson', self.durbin_watson, None))
        if self.ljung_box:
            tests.append(('Ljung-Box', self.ljung_box[0], self.ljung_box[1]))
        if self.reset_test:
            tests.append(('RESET', self.reset_test[0], self.reset_test[1]))
        if self.hausman_test:
            tests.append(('Hausman', self.hausman_test[0], self.hausman_test[1]))
        if self.pesaran_cd:
            tests.append(('Pesaran CD', self.pesaran_cd[0], self.pesaran_cd[1]))
        if self.wooldridge_ar1:
            tests.append(('Wooldridge AR(1)', self.wooldridge_ar1[0], self.wooldridge_ar1[1]))
            
        return pd.DataFrame(tests, columns=['Test', 'Statistic', 'P-value'])


@dataclass
class ModelComparison:
    """Container for model comparison metrics."""
    aic: float
    bic: float
    log_likelihood: float
    n_parameters: int
    
    # Out-of-sample performance
    rmse: Optional[float] = None
    mae: Optional[float] = None
    mape: Optional[float] = None
    
    # In-sample fit
    r_squared: float = 0.0
    adj_r_squared: float = 0.0
    
    def to_series(self) -> pd.Series:
        """Convert to pandas Series for easy comparison."""
        return pd.Series({
            'AIC': self.aic,
            'BIC': self.bic,
            'Log-Likelihood': self.log_likelihood,
            'Parameters': self.n_parameters,
            'R-squared': self.r_squared,
            'Adj. R-squared': self.adj_r_squared,
            'RMSE': self.rmse,
            'MAE': self.mae,
            'MAPE': self.mape
        })


class ResultsContainer:
    """Unified container for all three-tier results.
    
    This class provides a consistent interface for storing and accessing
    results from any tier of the analysis.
    """
    
    def __init__(self, tier: str, model_type: str):
        """Initialize results container.
        
        Parameters
        ----------
        tier : str
            Tier identifier (tier1_pooled, tier2_commodity, tier3_validation)
        model_type : str
            Specific model type within the tier
        """
        self.tier = tier
        self.model_type = model_type
        self.timestamp = pd.Timestamp.now()
        
        # Core results storage
        self.coefficients: Dict[str, float] = {}
        self.standard_errors: Dict[str, float] = {}
        self.confidence_intervals: Dict[str, Tuple[float, float]] = {}
        
        # Model fit
        self.fitted_values: Optional[pd.Series] = None
        self.residuals: Optional[pd.Series] = None
        self.sample_data: Optional[pd.DataFrame] = None
        
        # Diagnostics
        self.diagnostics = DiagnosticTests()
        self.comparison_metrics = None
        
        # Tier-specific storage
        self.tier_specific: Dict[str, Any] = {}
        
        # Metadata
        self.metadata: Dict[str, Any] = {
            'estimation_time': None,
            'n_observations': None,
            'convergence': None,
            'warnings': []
        }
    
    def add_coefficient(self, name: str, value: float, se: float, 
                       ci: Optional[Tuple[float, float]] = None) -> None:
        """Add a coefficient with its statistics.
        
        Parameters
        ----------
        name : str
            Variable name
        value : float
            Coefficient estimate
        se : float
            Standard error
        ci : tuple, optional
            Confidence interval (lower, upper)
        """
        self.coefficients[name] = value
        self.standard_errors[name] = se
        if ci:
            self.confidence_intervals[name] = ci
        else:
            # Default 95% CI
            self.confidence_intervals[name] = (
                value - 1.96 * se,
                value + 1.96 * se
            )
    
    def get_coefficients_table(self) -> pd.DataFrame:
        """Get coefficients as a formatted DataFrame.
        
        Returns
        -------
        pd.DataFrame
            Table with coefficients, SEs, t-stats, p-values, and CIs
        """
        data = []
        for var in self.coefficients:
            coef = self.coefficients[var]
            se = self.standard_errors.get(var, np.nan)
            t_stat = coef / se if se > 0 else np.nan
            # Two-tailed p-value
            p_value = 2 * (1 - pd.Series([abs(t_stat)]).apply(
                lambda x: 1 - 0.5 * (1 + np.sign(x) * (1 - np.exp(-2 * x**2 / np.pi)))
            ).iloc[0]) if not np.isnan(t_stat) else np.nan
            
            ci_lower, ci_upper = self.confidence_intervals.get(var, (np.nan, np.nan))
            
            data.append({
                'Variable': var,
                'Coefficient': coef,
                'Std. Error': se,
                't-statistic': t_stat,
                'P-value': p_value,
                'CI Lower': ci_lower,
                'CI Upper': ci_upper
            })
        
        return pd.DataFrame(data)
    
    def add_diagnostics(self, **kwargs) -> None:
        """Add diagnostic test results.
        
        Parameters
        ----------
        **kwargs
            Diagnostic test results as keyword arguments
        """
        for test_name, test_value in kwargs.items():
            if hasattr(self.diagnostics, test_name):
                setattr(self.diagnostics, test_name, test_value)
            else:
                info(f"Unknown diagnostic test: {test_name}")
    
    def set_comparison_metrics(self, n_obs: int, n_params: int, 
                              log_lik: float, r2: float) -> None:
        """Calculate and store model comparison metrics.
        
        Parameters
        ----------
        n_obs : int
            Number of observations
        n_params : int
            Number of parameters
        log_lik : float
            Log-likelihood
        r2 : float
            R-squared
        """
        # Information criteria
        aic = 2 * n_params - 2 * log_lik
        bic = n_params * np.log(n_obs) - 2 * log_lik
        
        # Adjusted R-squared
        adj_r2 = 1 - (1 - r2) * (n_obs - 1) / (n_obs - n_params - 1)
        
        self.comparison_metrics = ModelComparison(
            aic=aic,
            bic=bic,
            log_likelihood=log_lik,
            n_parameters=n_params,
            r_squared=r2,
            adj_r_squared=adj_r2
        )
    
    def add_tier_specific(self, **kwargs) -> None:
        """Add tier-specific results.
        
        Parameters
        ----------
        **kwargs
            Tier-specific results as keyword arguments
        """
        self.tier_specific.update(kwargs)
    
    def summary(self) -> str:
        """Generate text summary of results."""
        lines = [
            f"\n{self.tier.upper()} - {self.model_type} Results",
            "=" * 60,
            f"Estimated: {self.timestamp.strftime('%Y-%m-%d %H:%M:%S')}",
            ""
        ]
        
        # Model info
        if self.metadata['n_observations']:
            lines.append(f"Observations: {self.metadata['n_observations']}")
        if self.comparison_metrics:
            lines.append(f"R-squared: {self.comparison_metrics.r_squared:.4f}")
            lines.append(f"Adj. R-squared: {self.comparison_metrics.adj_r_squared:.4f}")
        
        # Coefficients table
        if self.coefficients:
            lines.append("\nCoefficients:")
            lines.append("-" * 60)
            coef_df = self.get_coefficients_table()
            lines.append(coef_df.to_string(index=False))
        
        # Diagnostic tests
        if any(getattr(self.diagnostics, attr) is not None 
               for attr in dir(self.diagnostics) if not attr.startswith('_')):
            lines.append("\nDiagnostic Tests:")
            lines.append("-" * 60)
            diag_df = self.diagnostics.to_dataframe()
            if not diag_df.empty:
                lines.append(diag_df.to_string(index=False))
        
        # Model comparison
        if self.comparison_metrics:
            lines.append("\nModel Selection Criteria:")
            lines.append("-" * 60)
            lines.append(f"AIC: {self.comparison_metrics.aic:.2f}")
            lines.append(f"BIC: {self.comparison_metrics.bic:.2f}")
            lines.append(f"Log-Likelihood: {self.comparison_metrics.log_likelihood:.2f}")
        
        # Warnings
        if self.metadata['warnings']:
            lines.append("\nWarnings:")
            lines.append("-" * 60)
            for warning in self.metadata['warnings']:
                lines.append(f"- {warning}")
        
        return "\n".join(lines)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert results to dictionary."""
        return {
            'tier': self.tier,
            'model_type': self.model_type,
            'timestamp': self.timestamp.isoformat(),
            'coefficients': self.coefficients,
            'standard_errors': self.standard_errors,
            'confidence_intervals': self.confidence_intervals,
            'diagnostics': asdict(self.diagnostics) if self.diagnostics else None,
            'comparison_metrics': asdict(self.comparison_metrics) if self.comparison_metrics else None,
            'tier_specific': self.tier_specific,
            'metadata': self.metadata
        }
    
    def save(self, path: Union[str, Path]) -> None:
        """Save results to file.
        
        Parameters
        ----------
        path : str or Path
            Output file path
        """
        path = Path(path)
        path.parent.mkdir(parents=True, exist_ok=True)
        
        if path.suffix == '.json':
            with open(path, 'w') as f:
                json.dump(self.to_dict(), f, indent=2, default=str)
        else:
            # Use pickle for complex objects
            import pickle
            with open(path, 'wb') as f:
                pickle.dump(self, f)
        
        info(f"Results saved to {path}")