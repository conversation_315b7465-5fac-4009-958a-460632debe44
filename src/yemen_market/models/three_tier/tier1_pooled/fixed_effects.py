"""Utilities for handling and extracting fixed effects from panel models.

This module provides functions to work with fixed effects, particularly those
estimated by `linearmodels.PanelOLS`.
"""

import pandas as pd
import numpy as np
from typing import List, Union, Optional, Dict

from linearmodels.panel.results import PanelEffectsResults

from yemen_market.utils.logging import (
    info, warning, error, timer, log_data_shape, bind
)

# Set module context
bind(module=__name__)


def extract_estimated_effects(results: PanelEffectsResults) -> Dict[str, pd.Series]:
    """Extract estimated fixed effects from a PanelOLS results object.

    Parameters
    ----------
    results : PanelEffectsResults
        The results object from a fitted linearmodels.PanelOLS model.

    Returns
    -------
    Dict[str, pd.Series]
        A dictionary where keys are effect types (e.g., 'entity_effects', 
        'time_effects') and values are pandas Series of the estimated effects.
    """
    with timer("extract_estimated_effects"):
        effects = {}
        if hasattr(results, 'estimated_effects') and results.estimated_effects is not None:
            info(f"Extracting fixed effects. Available: {list(results.estimated_effects.columns)}")
            # Effects are stored as columns in a DataFrame, index is original entity/time labels
            for effect_name in results.estimated_effects.columns:
                effects[effect_name.lower().replace(' ', '_')] = results.estimated_effects[effect_name]
                log_data_shape(f"{effect_name.lower().replace(' ', '_')}", effects[effect_name.lower().replace(' ', '_')])
        else:
            warning("No estimated_effects attribute found or it is None in the results object.")
        
        # linearmodels also stores time effects separately if estimated
        if hasattr(results, 'time_effects') and results.time_effects is not None:
            if 'time_effects' not in effects: # Avoid duplication if already in estimated_effects
                effects['time_effects'] = results.time_effects
                log_data_shape("time_effects_specific_attr", results.time_effects)
                info("Extracted time_effects from specific attribute.")
            
        # Similarly for entity effects
        if hasattr(results, 'entity_effects') and results.entity_effects is not None:
            # This is usually the main set of effects if entity_effects=True
            # It might be redundant if already captured by results.estimated_effects
            # but let's ensure it's available under a consistent key if not.
            if 'entity_effects' not in effects and (not hasattr(results, 'estimated_effects') or results.estimated_effects is None or 'estimated_effects' not in results.estimated_effects.columns):
                 effects['entity_effects'] = results.entity_effects
                 log_data_shape("entity_effects_specific_attr", results.entity_effects)
                 info("Extracted entity_effects from specific attribute.")

        if not effects:
            warning("Could not extract any fixed effects from the model results.")
            
        return effects

def within_transform(
    data: pd.DataFrame, 
    group_cols: Union[str, List[str]], 
    transform_cols: Optional[List[str]] = None
) -> pd.DataFrame:
    """Perform within transformation (demeaning) on specified columns.

    Subtracts the group mean from each observation within the group.

    Parameters
    ----------
    data : pd.DataFrame
        Input DataFrame.
    group_cols : Union[str, List[str]]
        Column(s) to group by for demeaning.
    transform_cols : Optional[List[str]], optional
        List of columns to transform. If None, all numeric columns 
        not in group_cols will be transformed. Defaults to None.

    Returns
    -------
    pd.DataFrame
        DataFrame with specified columns demeaned.
    """
    with timer(f"within_transform_by_{group_cols}"):
        if isinstance(group_cols, str):
            group_cols = [group_cols]
        
        if transform_cols is None:
            numeric_cols = data.select_dtypes(include=np.number).columns
            transform_cols = [col for col in numeric_cols if col not in group_cols]
            info(f"No transform_cols specified, selected numeric columns: {transform_cols}")

        if not transform_cols:
            warning("No columns selected for within transformation.")
            return data.copy()

        demeaned_data = data.copy()
        
        # Ensure group_cols exist
        for gc in group_cols:
            if gc not in demeaned_data.columns:
                error(f"Group column '{gc}' not found in DataFrame. Aborting within transformation.")
                return data # or raise ValueError

        grouped = demeaned_data.groupby(group_cols)

        for col in transform_cols:
            if col not in demeaned_data.columns:
                warning(f"Column {col} not found in DataFrame for transformation, skipping.")
                continue
            
            try:
                group_means = grouped[col].transform('mean')
                demeaned_data[f"{col}_within"] = demeaned_data[col] - group_means
                log_data_shape(f"{col}_within", demeaned_data[f"{col}_within"])
            except Exception as e:
                error(f"Error during within transformation for column '{col}': {e}")
        
        info(f"Performed within transformation on columns: {transform_cols}")
        return demeaned_data

# Placeholder for between/within decomposition - to be implemented if needed
# def decompose_variance(data: pd.DataFrame, group_col: str, value_col: str) -> Dict[str, float]:
#     """Decompose variance into within and between components."""
#     pass


class FixedEffectsHandler:
    """
    Handles operations related to fixed effects in panel data models.
    This class may encapsulate functions like within_transform and extract_estimated_effects,
    or provide other utilities for managing fixed effects.
    """
    def __init__(self, model_results: Optional[PanelEffectsResults] = None):
        self.model_results = model_results

    def get_effects(self) -> Dict[str, pd.Series]:
        """Extracts fixed effects from the stored model results."""
        if not self.model_results:
            warning("No model results provided to FixedEffectsHandler.")
            return {}
        return extract_estimated_effects(self.model_results)

    def apply_within_transform(
        self, 
        data: pd.DataFrame, 
        group_cols: Union[str, List[str]], 
        transform_cols: Optional[List[str]] = None
    ) -> pd.DataFrame:
        """Applies within transformation to the given data."""
        return within_transform(data, group_cols, transform_cols)

    # Add other methods as required by tests or functionality
